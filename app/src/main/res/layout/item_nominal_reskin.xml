<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:id="@+id/root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="6dp"
        android:padding="16dp"
        android:background="@drawable/bg_unselected_reskin"
        android:minHeight="64dp"
        android:orientation="vertical"
        android:gravity="center|left"
        android:clickable="true"
        android:focusable="true">

        <TextView
            android:id="@+id/text_nominal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="Rp5.000"
            android:textSize="16sp"
            style="@style/Body.lg.smbold"
            android:textColor="@color/text_black_default_ns"/>
    </LinearLayout>
</layout>