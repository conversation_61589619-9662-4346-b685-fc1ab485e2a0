<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:app="http://schemas.android.com/apk/res-auto"
		xmlns:tools="http://schemas.android.com/tools"
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		android:background="@drawable/bg_new_skin_activity_container"
		tools:context="id.co.bri.brimons.ui.activities.dompetdigitalreskin.FormDompetDigitalReskinActivity">

	<!-- Toolbar -->
	<include
			android:id="@+id/toolbar"
			layout="@layout/toolbar_new_skin"
			android:layout_width="match_parent"
			android:layout_height="wrap_content" />

	<androidx.coordinatorlayout.widget.CoordinatorLayout
			android:id="@+id/content"
			android:layout_width="match_parent"
			android:layout_height="match_parent">

		<androidx.core.widget.NestedScrollView
				android:id="@+id/nsv_content"
				android:layout_width="match_parent"
				android:layout_height="match_parent"
				android:layout_marginTop="70dp"
				android:paddingTop="@dimen/size_1dp"
				android:orientation="vertical"
				android:paddingBottom="100dp"
				android:background="@drawable/bg_card_rounded_ns"
				android:overScrollMode="never"
				>

			<LinearLayout
					android:id="@+id/ll_content"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:orientation="vertical"
					android:layout_margin="16dp">

				<LinearLayout
						android:id="@+id/ll_content_main"
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						android:orientation="vertical">

					<!-- Wilayah input -->
					<LinearLayout
							android:id="@+id/ll_wallet_selection"
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:orientation="horizontal"
							android:gravity="center_vertical|center_horizontal"
							android:layout_gravity="center_horizontal"
							android:minHeight="64dp"
							android:paddingHorizontal="@dimen/size_16dp"
							android:background="@drawable/selector_input_field_ns">

						<RelativeLayout
								android:id="@+id/icon_container"
								android:layout_width="@dimen/size_32dp"
								android:layout_height="@dimen/size_32dp"
								android:layout_marginStart="0dp"
								android:layout_marginEnd="8dp"
								android:layout_gravity="center_vertical"
								android:background="@drawable/round_icon_ns"
								android:visibility="gone">

							<ImageView
									android:id="@+id/iv_wallet_icon"
									android:layout_width="match_parent"
									android:layout_height="match_parent"
									android:layout_alignParentTop="true"
									android:src="@drawable/ikon_wilayah" />
						</RelativeLayout>

						<LinearLayout
								android:layout_width="match_parent"
								android:layout_height="wrap_content"
								android:orientation="horizontal">


							<LinearLayout
									android:layout_width="0dp"
									android:layout_height="wrap_content"
									android:layout_weight="1"
									android:orientation="vertical"
									android:layout_gravity="center_vertical"
									tools:ignore="RtlSymmetry">

								<TextView
										android:id="@+id/region_textview"
										style="@style/Body.md.reg"
										android:layout_width="match_parent"
										android:layout_height="wrap_content"
										android:textColor="@color/text_black_default_ns"
										android:layout_marginBottom="4dp"
										android:textSize="@dimen/size_12sp"
										android:text="@string/region"
										android:visibility="gone" />


								<com.google.android.material.textfield.TextInputLayout
										android:layout_width="match_parent"
										android:layout_height="wrap_content"
										app:hintEnabled="false"
										android:gravity="center_vertical"
										app:boxStrokeWidth="0dp">

									<EditText
											android:id="@+id/et_wallet"
											android:layout_width="match_parent"
											android:layout_height="wrap_content"
											android:hint="@string/jenis_e_wallet"
											android:focusable="false"
											android:textColorHint="@color/text_black_default_ns"
											android:layout_gravity="center_vertical"
											android:background="@android:color/transparent"
											android:paddingStart="0dp"
											android:paddingEnd="0dp"
											style="@style/Body.lg.reg"
											android:inputType="none|textNoSuggestions"
											android:paddingVertical="0dp" />
								</com.google.android.material.textfield.TextInputLayout>

							</LinearLayout>

							<ImageView
									android:layout_gravity="center_vertical"
									android:layout_width="@dimen/size_24dp"
									android:layout_height="@dimen/size_24dp"
									android:src="@drawable/ic_arrow_down_ns" />

						</LinearLayout>
					</LinearLayout>


					<!-- Nomor Pelanggan input -->
					<id.co.bri.brimons.ui.widget.input_til.BaseInputView
							android:id="@+id/et_phone_number"
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:layout_marginTop="21dp"
							app:baseInputTextStyle="@style/Body.lg.reg"
							app:prefixText="@string/hint_prefix_62"
							tools:hintText="@string/hint_label_no_hp" />

					<View
							android:layout_width="match_parent"
							android:layout_height="@dimen/size_1dp"
							android:layout_marginVertical="@dimen/size_21dp"
							android:background="@color/border_gray_soft_ns" />

				</LinearLayout>

				<!-- Tab Section for Favorit and Riwayat -->
				<LinearLayout
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						android:orientation="vertical">

					<LinearLayout
							android:id="@+id/searchview_briva"
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:paddingVertical="12dp"
							android:paddingHorizontal="16dp"
							android:gravity="center_vertical"
							android:orientation="horizontal"
							android:theme="@style/AppSearchViewSmall"
							android:background="@drawable/bg_input_black_100_brimo_ns_rounded"
							android:layout_marginBottom="24dp">

						<ImageView
								android:layout_width="wrap_content"
								android:layout_height="match_parent"
								android:src="@drawable/ic_search_new_skin_20"
								android:layout_marginEnd="8dp" />


						<TextView
								android:layout_width="wrap_content"
								android:layout_height="match_parent"
								android:text="@string/cari_pelanggan_atau_layanan"
								android:gravity="center_vertical"
								style="@style/Body.lg.reg" />
					</LinearLayout>

					<!-- Tab Layout -->
					<LinearLayout
							android:layout_width="wrap_content"
							android:layout_height="wrap_content"
							android:orientation="horizontal"
							android:layout_marginBottom="24dp">

						<TextView
								android:id="@+id/tab_terhubung"
								android:layout_width="wrap_content"
								android:layout_height="wrap_content"
								android:layout_weight="1"
								android:text="@string/txt_terhubung"
								style="@style/Body.md.reg"
								android:textAlignment="center"
								android:textColor="@color/black"
								android:background="@drawable/bg_input_black_100_brimo_ns_rounded"
								android:paddingVertical="9dp"
								android:paddingHorizontal="16dp"
								android:layout_marginEnd="8dp"
								android:clickable="true"
								android:focusable="true" />


						<TextView
								android:id="@+id/tab_favorit"
								android:layout_width="wrap_content"
								android:layout_height="wrap_content"
								android:layout_weight="1"
								android:text="@string/favorit"
								style="@style/Body.md.reg"
								android:textAlignment="center"
								android:textColor="@color/text_brand_primary_ns"
								android:background="@drawable/rounded_button_soft_ns"
								android:paddingVertical="9dp"
								android:paddingHorizontal="16dp"
								android:layout_marginHorizontal="8dp"
								android:clickable="true"
								android:focusable="true" />

						<TextView
								android:id="@+id/tab_riwayat"
								android:layout_width="wrap_content"
								android:layout_height="wrap_content"
								android:layout_weight="1"
								android:text="@string/txt_riwayat"
								style="@style/Body.md.reg"
								android:textAlignment="center"
								android:textColor="@color/black"
								android:background="@drawable/bg_input_black_100_brimo_ns_rounded"
								android:paddingVertical="9dp"
								android:paddingHorizontal="16dp"
								android:layout_marginStart="8dp"
								android:clickable="true"
								android:focusable="true" />


					</LinearLayout>

						<LinearLayout
								android:layout_width="match_parent"
								android:layout_height="wrap_content"
								android:orientation="vertical">

							<!-- Terhubung Content (Connected Wallets) -->
							<LinearLayout
									android:id="@+id/content_terhubung"
									android:layout_width="match_parent"
									android:layout_height="wrap_content"
									android:orientation="vertical"
									android:visibility="visible">

								<!-- Connected Wallets -->
								<LinearLayout
										android:id="@+id/ll_connected_wallets"
										android:layout_width="match_parent"
										android:layout_height="wrap_content"
										android:orientation="vertical"
										android:visibility="gone">

									<LinearLayout
											android:layout_width="match_parent"
											android:layout_height="wrap_content"
											android:orientation="horizontal"
											android:weightSum="4"
											android:layout_marginBottom="@dimen/size_16dp">

										<TextView
												android:layout_width="0dp"
												android:layout_height="wrap_content"
												android:text="@string/txt_e_wallet_terhubung"
												android:textSize="@dimen/size_14sp"
												android:layout_weight="3"
												style="@style/Body.lg.smbold"
												android:textColor="@color/black_ns_main" />

										<TextView
												android:id="@+id/tv_edit_dompet_digital"
												android:layout_width="0dp"
												android:layout_height="wrap_content"
												android:text="@string/edit"
												android:textSize="@dimen/size_14sp"
												android:textColor="@color/primary_ns_main"
												style="@style/Body.lg.smbold"
												android:textAlignment="textEnd"
												android:layout_weight="1" />

									</LinearLayout>

									<androidx.recyclerview.widget.RecyclerView
											android:id="@+id/rv_connected_wallets"
											android:layout_width="match_parent"
											android:layout_height="wrap_content"
											android:layoutAnimation="@anim/layout_animation_fade_in" />
								</LinearLayout>

								<!-- No Connected Wallets Message -->
								<LinearLayout
										android:id="@+id/ll_no_connected_wallets"
										android:layout_width="match_parent"
										android:layout_height="wrap_content"
										android:orientation="vertical"
										android:gravity="start"
										android:padding="20dp"
										android:background="@drawable/bg_input_black_100_brimo_ns"
										android:visibility="visible">

									<TextView
											android:layout_width="wrap_content"
											android:layout_height="wrap_content"
											android:text="@string/topup_e_wallet_txt"
											style="@style/Body.lg.reg"
											android:textColor="@color/black_ns_main"
											android:layout_marginBottom="12dp" />

									<LinearLayout
											android:layout_width="match_parent"
											android:layout_height="wrap_content"
											android:orientation="horizontal"
											android:gravity="center_vertical">

										<RelativeLayout
												android:layout_width="wrap_content"
												android:layout_height="wrap_content"
												android:layout_weight="1">

											<ImageView
													android:id="@+id/list_1"
													android:layout_width="32dp"
													android:layout_height="32dp"
													android:layout_toEndOf="@+id/list_2"
													android:src="@drawable/ic_plus_rounded_ns"
													android:layout_marginStart="-12dp" />

											<ImageView
													android:id="@+id/list_2"
													android:layout_width="32dp"
													android:layout_height="32dp"
													android:layout_toEndOf="@+id/list_3"
													android:src="@drawable/ic_shopee_pay"
													android:layout_marginStart="-12dp" />

											<ImageView
													android:id="@+id/list_3"
													android:layout_width="32dp"
													android:layout_height="32dp"
													android:layout_toEndOf="@+id/list_4"
													android:src="@drawable/ic_ovo"
													android:layout_marginStart="-12dp" />

											<ImageView
													android:id="@+id/list_4"
													android:layout_width="32dp"
													android:layout_height="32dp"
													android:layout_toEndOf="@+id/list_5"
													android:src="@drawable/ic_dana"
													android:layout_marginStart="-12dp" />

											<ImageView
													android:id="@+id/list_5"
													android:layout_width="32dp"
													android:layout_height="32dp"
													android:src="@drawable/ic_link_aja" />

										</RelativeLayout>

										<Button
												android:id="@+id/btnConnect"
												android:layout_width="wrap_content"
												android:layout_height="wrap_content"
												android:minHeight="32dp"
												android:minWidth="115dp"
												style="@style/CustomButtonStyle"
												android:text="Hubungkan"
												android:textSize="12sp"
												android:textAllCaps="false"
												android:background="@drawable/rounded_button_ns"
												android:textColor="@color/selector_text_color_button_primary_ns" />
										</LinearLayout>


								</LinearLayout>
							</LinearLayout>

							<!-- Favorit Content (Saved List) -->
							<LinearLayout
									android:id="@+id/content_favorit"
									android:layout_width="match_parent"
									android:layout_height="wrap_content"
									android:orientation="vertical"
									android:visibility="gone">

								<!-- Saved List RecyclerView -->
								<androidx.recyclerview.widget.RecyclerView
										android:id="@+id/rv_daftar_favorit"
										android:layout_width="match_parent"
										android:layout_height="wrap_content"
										android:layoutAnimation="@anim/layout_animation_fade_in"
										android:visibility="visible" />

								<!-- No Saved Data Message -->
								<LinearLayout
										android:id="@+id/ll_no_data_saved"
										android:layout_width="match_parent"
										android:layout_height="wrap_content"
										android:orientation="vertical"
										android:gravity="center"
										android:padding="32dp"
										android:visibility="gone">

									<ImageView
											android:layout_width="120dp"
											android:layout_height="120dp"
											android:src="@drawable/empty_box_3d"
											android:layout_marginBottom="16dp" />

									<TextView
											android:id="@+id/no_data_saved"
											android:layout_width="wrap_content"
											android:layout_height="wrap_content"
											android:text="@string/no_fav_trx_title"
											android:textStyle="bold"
											android:textColor="@color/black"
											android:layout_marginTop="8dp" />

									<TextView
											android:layout_width="wrap_content"
											android:layout_height="wrap_content"
											android:text="@string/no_fav_trx_desc"
											android:textSize="12sp"
											android:gravity="center"
											android:layout_marginTop="4dp" />
								</LinearLayout>
							</LinearLayout>

							<!-- Riwayat Content (History) -->
							<LinearLayout
									android:id="@+id/content_riwayat"
									android:layout_width="match_parent"
									android:layout_height="wrap_content"
									android:orientation="vertical"
									android:visibility="gone">

								<!-- History RecyclerView -->
								<androidx.recyclerview.widget.RecyclerView
										android:id="@+id/rv_riwayat"
										android:layout_width="match_parent"
										android:layout_height="wrap_content"
										android:nestedScrollingEnabled="false"
										android:layoutAnimation="@anim/layout_animation_fade_in"
										android:visibility="visible" />

								<!-- No History Message -->
								<LinearLayout
										android:id="@+id/ll_no_history"
										android:layout_width="match_parent"
										android:layout_height="wrap_content"
										android:orientation="vertical"
										android:gravity="center"
										android:padding="32dp"
										android:visibility="gone">

									<ImageView
											android:layout_width="120dp"
											android:layout_height="120dp"
											android:src="@drawable/empty_box_3d"
											android:layout_marginBottom="16dp" />

									<TextView
											android:id="@+id/tv_no_history"
											android:layout_width="wrap_content"
											android:layout_height="wrap_content"
											android:text="@string/no_history_trx_title"
											android:textStyle="bold"
											android:textColor="@color/black"
											android:layout_marginTop="8dp" />

									<TextView
											android:layout_width="wrap_content"
											android:layout_height="wrap_content"
											android:text="@string/no_history_trx_desc"
											android:textSize="12sp"
											android:gravity="center"
											android:layout_marginTop="4dp" />
								</LinearLayout>
							</LinearLayout>
						</LinearLayout>
					</LinearLayout>
				</LinearLayout>

		</androidx.core.widget.NestedScrollView>
	</androidx.coordinatorlayout.widget.CoordinatorLayout>


	<!-- Bottom Button -->
	<LinearLayout
			android:id="@+id/btn_container"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:layout_gravity="bottom"
			android:orientation="vertical"
			android:background="@android:color/white">

		<View
				android:id="@+id/bottom_border"
				android:layout_width="wrap_content"
				android:layout_height="@dimen/size_1dp"
				android:background="@color/border_gray_soft_ns" />

		<LinearLayout
				android:layout_width="match_parent"
				android:layout_height="wrap_content"
				android:orientation="horizontal"
				android:gravity="center_vertical"
				android:padding="16dp">

			<Button
					android:id="@+id/btnSubmit"
					android:layout_width="match_parent"
					android:layout_height="56dp"
					style="@style/CustomButtonStyle"
					android:text="Lanjutkan"
					android:textSize="16sp"
					android:textAllCaps="false"
					android:background="@drawable/rounded_button_ns"
					android:textColor="@color/selector_text_color_button_primary_ns"
					android:enabled="false" />

		</LinearLayout>

	</LinearLayout>

</FrameLayout>